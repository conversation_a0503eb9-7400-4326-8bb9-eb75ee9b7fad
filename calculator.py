#!/usr/bin/env python3
"""
Simple Calculator Application
Supports basic arithmetic operations: +, -, *, /, %, ** (power)
"""

import math
import sys


class Calculator:
    """A simple calculator class with basic arithmetic operations."""
    
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        """Add two numbers."""
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def subtract(self, a, b):
        """Subtract b from a."""
        result = a - b
        self.history.append(f"{a} - {b} = {result}")
        return result
    
    def multiply(self, a, b):
        """Multiply two numbers."""
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def divide(self, a, b):
        """Divide a by b."""
        if b == 0:
            raise ValueError("Cannot divide by zero!")
        result = a / b
        self.history.append(f"{a} / {b} = {result}")
        return result
    
    def modulo(self, a, b):
        """Get remainder of a divided by b."""
        if b == 0:
            raise ValueError("Cannot divide by zero!")
        result = a % b
        self.history.append(f"{a} % {b} = {result}")
        return result
    
    def power(self, a, b):
        """Raise a to the power of b."""
        result = a ** b
        self.history.append(f"{a} ** {b} = {result}")
        return result
    
    def square_root(self, a):
        """Calculate square root of a."""
        if a < 0:
            raise ValueError("Cannot calculate square root of negative number!")
        result = math.sqrt(a)
        self.history.append(f"√{a} = {result}")
        return result
    
    def get_history(self):
        """Return calculation history."""
        return self.history
    
    def clear_history(self):
        """Clear calculation history."""
        self.history = []
    
    def evaluate_expression(self, expression):
        """Safely evaluate a mathematical expression."""
        try:
            # Replace common mathematical symbols
            expression = expression.replace('^', '**')
            expression = expression.replace('√', 'math.sqrt')
            
            # Only allow safe mathematical operations
            allowed_names = {
                k: v for k, v in math.__dict__.items() if not k.startswith("__")
            }
            allowed_names.update({"abs": abs, "round": round})
            
            result = eval(expression, {"__builtins__": {}}, allowed_names)
            self.history.append(f"{expression} = {result}")
            return result
        except Exception as e:
            raise ValueError(f"Invalid expression: {e}")


def print_menu():
    """Print the calculator menu."""
    print("\n" + "="*50)
    print("           PYTHON CALCULATOR")
    print("="*50)
    print("1. Addition (+)")
    print("2. Subtraction (-)")
    print("3. Multiplication (*)")
    print("4. Division (/)")
    print("5. Modulo (%)")
    print("6. Power (**)")
    print("7. Square Root (√)")
    print("8. Evaluate Expression")
    print("9. Show History")
    print("10. Clear History")
    print("0. Exit")
    print("="*50)


def get_number(prompt):
    """Get a valid number from user input."""
    while True:
        try:
            return float(input(prompt))
        except ValueError:
            print("Please enter a valid number!")


def main():
    """Main calculator function."""
    calc = Calculator()
    
    print("Welcome to Python Calculator!")
    
    while True:
        print_menu()
        
        try:
            choice = input("\nEnter your choice (0-10): ").strip()
            
            if choice == '0':
                print("Thank you for using Python Calculator!")
                break
            
            elif choice == '1':  # Addition
                a = get_number("Enter first number: ")
                b = get_number("Enter second number: ")
                result = calc.add(a, b)
                print(f"Result: {a} + {b} = {result}")
            
            elif choice == '2':  # Subtraction
                a = get_number("Enter first number: ")
                b = get_number("Enter second number: ")
                result = calc.subtract(a, b)
                print(f"Result: {a} - {b} = {result}")
            
            elif choice == '3':  # Multiplication
                a = get_number("Enter first number: ")
                b = get_number("Enter second number: ")
                result = calc.multiply(a, b)
                print(f"Result: {a} * {b} = {result}")
            
            elif choice == '4':  # Division
                a = get_number("Enter first number: ")
                b = get_number("Enter second number: ")
                try:
                    result = calc.divide(a, b)
                    print(f"Result: {a} / {b} = {result}")
                except ValueError as e:
                    print(f"Error: {e}")
            
            elif choice == '5':  # Modulo
                a = get_number("Enter first number: ")
                b = get_number("Enter second number: ")
                try:
                    result = calc.modulo(a, b)
                    print(f"Result: {a} % {b} = {result}")
                except ValueError as e:
                    print(f"Error: {e}")
            
            elif choice == '6':  # Power
                a = get_number("Enter base number: ")
                b = get_number("Enter exponent: ")
                result = calc.power(a, b)
                print(f"Result: {a} ** {b} = {result}")
            
            elif choice == '7':  # Square Root
                a = get_number("Enter number: ")
                try:
                    result = calc.square_root(a)
                    print(f"Result: √{a} = {result}")
                except ValueError as e:
                    print(f"Error: {e}")
            
            elif choice == '8':  # Evaluate Expression
                expression = input("Enter mathematical expression: ").strip()
                try:
                    result = calc.evaluate_expression(expression)
                    print(f"Result: {result}")
                except ValueError as e:
                    print(f"Error: {e}")
            
            elif choice == '9':  # Show History
                history = calc.get_history()
                if history:
                    print("\nCalculation History:")
                    print("-" * 30)
                    for i, calculation in enumerate(history, 1):
                        print(f"{i}. {calculation}")
                else:
                    print("No calculations in history.")
            
            elif choice == '10':  # Clear History
                calc.clear_history()
                print("History cleared!")
            
            else:
                print("Invalid choice! Please select a number from 0-10.")
        
        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break
        except Exception as e:
            print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()
