#!/usr/bin/env python3
"""
GUI Calculator using tkinter
A simple calculator with a graphical user interface
"""

import tkinter as tk
from tkinter import messagebox
import math


class CalculatorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Python Calculator")
        self.root.geometry("400x500")
        self.root.resizable(False, False)
        
        # Variables
        self.current = "0"
        self.previous = ""
        self.operator = ""
        self.result_displayed = False
        
        self.create_widgets()
    
    def create_widgets(self):
        # Display frame
        display_frame = tk.Frame(self.root, bg="black", padx=10, pady=10)
        display_frame.pack(fill=tk.BOTH, expand=True)
        
        # Display
        self.display = tk.Label(
            display_frame,
            text="0",
            font=("Arial", 24, "bold"),
            bg="black",
            fg="white",
            anchor="e",
            padx=10,
            pady=10
        )
        self.display.pack(fill=tk.BOTH, expand=True)
        
        # Buttons frame
        buttons_frame = tk.Frame(self.root, bg="gray20")
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Button configuration
        button_config = {
            'font': ('Arial', 16, 'bold'),
            'width': 5,
            'height': 2,
            'relief': 'raised',
            'bd': 2
        }
        
        # Row 0: Clear, +/-, %, ÷
        tk.Button(buttons_frame, text="C", bg="orange", fg="white", 
                 command=self.clear, **button_config).grid(row=0, column=0, padx=2, pady=2)
        tk.Button(buttons_frame, text="±", bg="orange", fg="white", 
                 command=self.toggle_sign, **button_config).grid(row=0, column=1, padx=2, pady=2)
        tk.Button(buttons_frame, text="%", bg="orange", fg="white", 
                 command=lambda: self.operator_click("%"), **button_config).grid(row=0, column=2, padx=2, pady=2)
        tk.Button(buttons_frame, text="÷", bg="orange", fg="white", 
                 command=lambda: self.operator_click("/"), **button_config).grid(row=0, column=3, padx=2, pady=2)
        
        # Row 1: 7, 8, 9, ×
        tk.Button(buttons_frame, text="7", bg="gray40", fg="white", 
                 command=lambda: self.number_click("7"), **button_config).grid(row=1, column=0, padx=2, pady=2)
        tk.Button(buttons_frame, text="8", bg="gray40", fg="white", 
                 command=lambda: self.number_click("8"), **button_config).grid(row=1, column=1, padx=2, pady=2)
        tk.Button(buttons_frame, text="9", bg="gray40", fg="white", 
                 command=lambda: self.number_click("9"), **button_config).grid(row=1, column=2, padx=2, pady=2)
        tk.Button(buttons_frame, text="×", bg="orange", fg="white", 
                 command=lambda: self.operator_click("*"), **button_config).grid(row=1, column=3, padx=2, pady=2)
        
        # Row 2: 4, 5, 6, -
        tk.Button(buttons_frame, text="4", bg="gray40", fg="white", 
                 command=lambda: self.number_click("4"), **button_config).grid(row=2, column=0, padx=2, pady=2)
        tk.Button(buttons_frame, text="5", bg="gray40", fg="white", 
                 command=lambda: self.number_click("5"), **button_config).grid(row=2, column=1, padx=2, pady=2)
        tk.Button(buttons_frame, text="6", bg="gray40", fg="white", 
                 command=lambda: self.number_click("6"), **button_config).grid(row=2, column=2, padx=2, pady=2)
        tk.Button(buttons_frame, text="−", bg="orange", fg="white", 
                 command=lambda: self.operator_click("-"), **button_config).grid(row=2, column=3, padx=2, pady=2)
        
        # Row 3: 1, 2, 3, +
        tk.Button(buttons_frame, text="1", bg="gray40", fg="white", 
                 command=lambda: self.number_click("1"), **button_config).grid(row=3, column=0, padx=2, pady=2)
        tk.Button(buttons_frame, text="2", bg="gray40", fg="white", 
                 command=lambda: self.number_click("2"), **button_config).grid(row=3, column=1, padx=2, pady=2)
        tk.Button(buttons_frame, text="3", bg="gray40", fg="white", 
                 command=lambda: self.number_click("3"), **button_config).grid(row=3, column=2, padx=2, pady=2)
        tk.Button(buttons_frame, text="+", bg="orange", fg="white", 
                 command=lambda: self.operator_click("+"), **button_config).grid(row=3, column=3, padx=2, pady=2)
        
        # Row 4: 0, ., =
        tk.Button(buttons_frame, text="0", bg="gray40", fg="white", 
                 command=lambda: self.number_click("0"), **button_config).grid(row=4, column=0, columnspan=2, padx=2, pady=2, sticky="ew")
        tk.Button(buttons_frame, text=".", bg="gray40", fg="white", 
                 command=self.decimal_click, **button_config).grid(row=4, column=2, padx=2, pady=2)
        tk.Button(buttons_frame, text="=", bg="orange", fg="white", 
                 command=self.equals_click, **button_config).grid(row=4, column=3, padx=2, pady=2)
        
        # Row 5: Additional functions
        tk.Button(buttons_frame, text="√", bg="lightblue", fg="black", 
                 command=self.square_root, **button_config).grid(row=5, column=0, padx=2, pady=2)
        tk.Button(buttons_frame, text="x²", bg="lightblue", fg="black", 
                 command=self.square, **button_config).grid(row=5, column=1, padx=2, pady=2)
        tk.Button(buttons_frame, text="1/x", bg="lightblue", fg="black", 
                 command=self.reciprocal, **button_config).grid(row=5, column=2, padx=2, pady=2)
        tk.Button(buttons_frame, text="CE", bg="red", fg="white", 
                 command=self.clear_entry, **button_config).grid(row=5, column=3, padx=2, pady=2)
        
        # Configure grid weights
        for i in range(4):
            buttons_frame.columnconfigure(i, weight=1)
    
    def update_display(self):
        """Update the calculator display."""
        if len(self.current) > 12:
            self.display.config(text=f"{float(self.current):.6e}")
        else:
            self.display.config(text=self.current)
    
    def number_click(self, number):
        """Handle number button clicks."""
        if self.result_displayed:
            self.current = "0"
            self.result_displayed = False
        
        if self.current == "0":
            self.current = number
        else:
            self.current += number
        
        self.update_display()
    
    def decimal_click(self):
        """Handle decimal point button click."""
        if self.result_displayed:
            self.current = "0"
            self.result_displayed = False
        
        if "." not in self.current:
            self.current += "."
        
        self.update_display()
    
    def operator_click(self, op):
        """Handle operator button clicks."""
        if self.operator and not self.result_displayed:
            self.equals_click()
        
        self.previous = self.current
        self.operator = op
        self.current = "0"
        self.result_displayed = False
    
    def equals_click(self):
        """Handle equals button click."""
        if self.operator and self.previous:
            try:
                prev_num = float(self.previous)
                curr_num = float(self.current)
                
                if self.operator == "+":
                    result = prev_num + curr_num
                elif self.operator == "-":
                    result = prev_num - curr_num
                elif self.operator == "*":
                    result = prev_num * curr_num
                elif self.operator == "/":
                    if curr_num == 0:
                        raise ValueError("Cannot divide by zero")
                    result = prev_num / curr_num
                elif self.operator == "%":
                    if curr_num == 0:
                        raise ValueError("Cannot divide by zero")
                    result = prev_num % curr_num
                
                self.current = str(result)
                self.operator = ""
                self.previous = ""
                self.result_displayed = True
                self.update_display()
                
            except Exception as e:
                messagebox.showerror("Error", str(e))
                self.clear()
    
    def clear(self):
        """Clear all values."""
        self.current = "0"
        self.previous = ""
        self.operator = ""
        self.result_displayed = False
        self.update_display()
    
    def clear_entry(self):
        """Clear current entry only."""
        self.current = "0"
        self.update_display()
    
    def toggle_sign(self):
        """Toggle the sign of current number."""
        if self.current != "0":
            if self.current.startswith("-"):
                self.current = self.current[1:]
            else:
                self.current = "-" + self.current
        self.update_display()
    
    def square_root(self):
        """Calculate square root of current number."""
        try:
            num = float(self.current)
            if num < 0:
                raise ValueError("Cannot calculate square root of negative number")
            result = math.sqrt(num)
            self.current = str(result)
            self.result_displayed = True
            self.update_display()
        except Exception as e:
            messagebox.showerror("Error", str(e))
    
    def square(self):
        """Calculate square of current number."""
        try:
            num = float(self.current)
            result = num ** 2
            self.current = str(result)
            self.result_displayed = True
            self.update_display()
        except Exception as e:
            messagebox.showerror("Error", str(e))
    
    def reciprocal(self):
        """Calculate reciprocal of current number."""
        try:
            num = float(self.current)
            if num == 0:
                raise ValueError("Cannot divide by zero")
            result = 1 / num
            self.current = str(result)
            self.result_displayed = True
            self.update_display()
        except Exception as e:
            messagebox.showerror("Error", str(e))


def main():
    root = tk.Tk()
    app = CalculatorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
