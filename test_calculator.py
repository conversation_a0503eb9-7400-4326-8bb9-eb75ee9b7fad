#!/usr/bin/env python3
"""
Test file for the Calculator class
Run this to verify that the calculator functions work correctly
"""

import unittest
import math
from calculator import Calculator


class TestCalculator(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.calc = Calculator()
    
    def test_addition(self):
        """Test addition operation."""
        self.assertEqual(self.calc.add(2, 3), 5)
        self.assertEqual(self.calc.add(-1, 1), 0)
        self.assertEqual(self.calc.add(0, 0), 0)
        self.assertEqual(self.calc.add(1.5, 2.5), 4.0)
    
    def test_subtraction(self):
        """Test subtraction operation."""
        self.assertEqual(self.calc.subtract(5, 3), 2)
        self.assertEqual(self.calc.subtract(1, 1), 0)
        self.assertEqual(self.calc.subtract(0, 5), -5)
        self.assertEqual(self.calc.subtract(2.5, 1.5), 1.0)
    
    def test_multiplication(self):
        """Test multiplication operation."""
        self.assertEqual(self.calc.multiply(3, 4), 12)
        self.assertEqual(self.calc.multiply(-2, 3), -6)
        self.assertEqual(self.calc.multiply(0, 100), 0)
        self.assertEqual(self.calc.multiply(2.5, 2), 5.0)
    
    def test_division(self):
        """Test division operation."""
        self.assertEqual(self.calc.divide(10, 2), 5)
        self.assertEqual(self.calc.divide(7, 2), 3.5)
        self.assertEqual(self.calc.divide(-6, 3), -2)
        
        # Test division by zero
        with self.assertRaises(ValueError):
            self.calc.divide(5, 0)
    
    def test_modulo(self):
        """Test modulo operation."""
        self.assertEqual(self.calc.modulo(10, 3), 1)
        self.assertEqual(self.calc.modulo(15, 5), 0)
        self.assertEqual(self.calc.modulo(7, 2), 1)
        
        # Test modulo by zero
        with self.assertRaises(ValueError):
            self.calc.modulo(5, 0)
    
    def test_power(self):
        """Test power operation."""
        self.assertEqual(self.calc.power(2, 3), 8)
        self.assertEqual(self.calc.power(5, 0), 1)
        self.assertEqual(self.calc.power(4, 0.5), 2)
        self.assertEqual(self.calc.power(-2, 2), 4)
    
    def test_square_root(self):
        """Test square root operation."""
        self.assertEqual(self.calc.square_root(9), 3)
        self.assertEqual(self.calc.square_root(16), 4)
        self.assertAlmostEqual(self.calc.square_root(2), math.sqrt(2))
        
        # Test square root of negative number
        with self.assertRaises(ValueError):
            self.calc.square_root(-1)
    
    def test_evaluate_expression(self):
        """Test expression evaluation."""
        self.assertEqual(self.calc.evaluate_expression("2 + 3 * 4"), 14)
        self.assertEqual(self.calc.evaluate_expression("(2 + 3) * 4"), 20)
        self.assertAlmostEqual(self.calc.evaluate_expression("sqrt(16)"), 4)
        self.assertEqual(self.calc.evaluate_expression("2 ** 3"), 8)
        
        # Test invalid expression
        with self.assertRaises(ValueError):
            self.calc.evaluate_expression("2 + + 3")
    
    def test_history(self):
        """Test calculation history functionality."""
        # Initially, history should be empty
        self.assertEqual(len(self.calc.get_history()), 0)
        
        # Perform some calculations
        self.calc.add(2, 3)
        self.calc.multiply(4, 5)
        
        # Check history
        history = self.calc.get_history()
        self.assertEqual(len(history), 2)
        self.assertIn("2 + 3 = 5", history[0])
        self.assertIn("4 * 5 = 20", history[1])
        
        # Clear history
        self.calc.clear_history()
        self.assertEqual(len(self.calc.get_history()), 0)


def run_manual_tests():
    """Run some manual tests to demonstrate calculator functionality."""
    print("Running manual tests...")
    calc = Calculator()
    
    print("\n=== Basic Operations ===")
    print(f"5 + 3 = {calc.add(5, 3)}")
    print(f"10 - 4 = {calc.subtract(10, 4)}")
    print(f"6 * 7 = {calc.multiply(6, 7)}")
    print(f"15 / 3 = {calc.divide(15, 3)}")
    print(f"17 % 5 = {calc.modulo(17, 5)}")
    print(f"2 ** 4 = {calc.power(2, 4)}")
    print(f"√25 = {calc.square_root(25)}")
    
    print("\n=== Expression Evaluation ===")
    print(f"2 + 3 * 4 = {calc.evaluate_expression('2 + 3 * 4')}")
    print(f"(2 + 3) * 4 = {calc.evaluate_expression('(2 + 3) * 4')}")
    print(f"sqrt(16) + 2 = {calc.evaluate_expression('sqrt(16) + 2')}")
    
    print("\n=== Calculation History ===")
    history = calc.get_history()
    for i, calculation in enumerate(history, 1):
        print(f"{i}. {calculation}")
    
    print(f"\nTotal calculations performed: {len(history)}")


if __name__ == "__main__":
    print("Testing Calculator...")
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run manual tests
    run_manual_tests()
