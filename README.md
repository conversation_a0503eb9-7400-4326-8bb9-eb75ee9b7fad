# Python Calculator

A comprehensive calculator application with both command-line and GUI interfaces.

## 📋 Prerequisites

You need Python installed on your system. If you don't have Python installed:

### Windows:
1. Go to [python.org](https://python.org/downloads/)
2. Download Python 3.8 or later
3. Run the installer and **make sure to check "Add Python to PATH"**
4. Restart your command prompt/terminal

### Alternative for Windows:
```bash
# Install from Microsoft Store
python
```

## 📁 Files

- `calculator.py` - Command-line calculator with interactive menu
- `calculator_gui.py` - GUI calculator using tkinter
- `test_calculator.py` - Test suite for the calculator
- `README.md` - This file

## 🚀 How to Run

### Command-Line Calculator
```bash
python calculator.py
```

Features:
- Interactive menu with numbered options
- Basic arithmetic operations (+, -, *, /, %, **)
- Square root calculation
- Expression evaluation (e.g., "2 + 3 * 4")
- Calculation history
- Error handling

### GUI Calculator
```bash
python calculator_gui.py
```

Features:
- Modern calculator interface
- Click buttons to perform calculations
- Additional functions: √, x², 1/x
- Clear (C) and Clear Entry (CE) buttons
- Sign toggle (±)

### Run Tests
```bash
python test_calculator.py
```

## 🎯 Usage Examples

### Command-Line Calculator
```
Welcome to Python Calculator!

==================================================
           PYTHON CALCULATOR
==================================================
1. Addition (+)
2. Subtraction (-)
3. Multiplication (*)
4. Division (/)
5. Modulo (%)
6. Power (**)
7. Square Root (√)
8. Evaluate Expression
9. Show History
10. Clear History
0. Exit
==================================================

Enter your choice (0-10): 1
Enter first number: 15
Enter second number: 25
Result: 15.0 + 25.0 = 40.0
```

### Expression Evaluation
You can evaluate complex mathematical expressions:
- `2 + 3 * 4` → 14
- `(2 + 3) * 4` → 20
- `sqrt(16) + 2` → 6
- `2 ** 3` → 8

## 🧪 Testing

The calculator includes comprehensive tests:

```bash
python test_calculator.py
```

Tests cover:
- All basic operations
- Error handling (division by zero, negative square roots)
- Expression evaluation
- History functionality

## 🔧 Troubleshooting

### "Python was not found" error:
1. Make sure Python is installed
2. Make sure Python is added to your system PATH
3. Try using `py` instead of `python` on Windows
4. Restart your terminal after installing Python

### GUI doesn't open:
- tkinter comes with Python by default
- If you get import errors, you might need to install tkinter:
  ```bash
  # On Ubuntu/Debian
  sudo apt-get install python3-tk
  
  # On macOS with Homebrew
  brew install python-tk
  ```

## 📝 Features

### Calculator Class Features:
- ✅ Addition, Subtraction, Multiplication, Division
- ✅ Modulo and Power operations
- ✅ Square root calculation
- ✅ Expression evaluation with math functions
- ✅ Calculation history tracking
- ✅ Error handling and validation

### GUI Features:
- ✅ Standard calculator layout
- ✅ Number pad (0-9)
- ✅ Decimal point support
- ✅ All basic operations
- ✅ Advanced functions (√, x², 1/x)
- ✅ Clear and Clear Entry
- ✅ Sign toggle
- ✅ Error dialogs

## 🎨 Customization

You can easily extend the calculator by:
1. Adding new operations to the `Calculator` class
2. Adding new buttons to the GUI
3. Implementing additional mathematical functions
4. Customizing the appearance and colors

Enjoy your Python calculator! 🧮✨
