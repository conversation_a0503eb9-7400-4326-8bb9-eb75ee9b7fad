#!/usr/bin/env python3
"""
Demo script to show calculator functionality
This demonstrates the calculator without requiring user input
"""

from calculator import Calculator

def demo_calculator():
    """Demonstrate calculator functionality."""
    print("🧮 Python Calculator Demo")
    print("=" * 50)
    
    calc = Calculator()
    
    print("\n📊 Basic Operations:")
    print("-" * 30)
    
    # Basic operations
    result1 = calc.add(15, 25)
    print(f"Addition: 15 + 25 = {result1}")
    
    result2 = calc.subtract(50, 18)
    print(f"Subtraction: 50 - 18 = {result2}")
    
    result3 = calc.multiply(7, 8)
    print(f"Multiplication: 7 × 8 = {result3}")
    
    result4 = calc.divide(84, 12)
    print(f"Division: 84 ÷ 12 = {result4}")
    
    result5 = calc.modulo(17, 5)
    print(f"Modulo: 17 % 5 = {result5}")
    
    result6 = calc.power(3, 4)
    print(f"Power: 3^4 = {result6}")
    
    result7 = calc.square_root(64)
    print(f"Square Root: √64 = {result7}")
    
    print("\n🔬 Advanced Expression Evaluation:")
    print("-" * 40)
    
    # Expression evaluation
    expr1 = calc.evaluate_expression("2 + 3 * 4")
    print(f"Expression: 2 + 3 * 4 = {expr1}")
    
    expr2 = calc.evaluate_expression("(10 + 5) * 2")
    print(f"Expression: (10 + 5) * 2 = {expr2}")
    
    expr3 = calc.evaluate_expression("sqrt(16) + pow(2, 3)")
    print(f"Expression: sqrt(16) + pow(2, 3) = {expr3}")
    
    expr4 = calc.evaluate_expression("abs(-15) + round(3.7)")
    print(f"Expression: abs(-15) + round(3.7) = {expr4}")
    
    print("\n📝 Calculation History:")
    print("-" * 30)
    
    history = calc.get_history()
    for i, calculation in enumerate(history, 1):
        print(f"{i:2d}. {calculation}")
    
    print(f"\n✅ Total calculations performed: {len(history)}")
    
    print("\n🎯 Error Handling Demo:")
    print("-" * 30)
    
    # Demonstrate error handling
    try:
        calc.divide(10, 0)
    except ValueError as e:
        print(f"Division by zero error: {e}")
    
    try:
        calc.square_root(-4)
    except ValueError as e:
        print(f"Negative square root error: {e}")
    
    try:
        calc.evaluate_expression("invalid + expression")
    except ValueError as e:
        print(f"Invalid expression error: {e}")
    
    print("\n🎉 Demo completed successfully!")
    print("=" * 50)
    
    print("\n📚 How to use your calculator:")
    print("• Command-line: py calculator.py")
    print("• GUI version: py calculator_gui.py")
    print("• Run tests: py test_calculator.py")

if __name__ == "__main__":
    demo_calculator()
